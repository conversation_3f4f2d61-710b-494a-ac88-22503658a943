"use client";
import { useAuthStore } from "@/stores/auth-store";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { FullScreenLoader } from "@/components/ui/FullScreenLoader";

export default function PrivateLayout({ children }: { children: React.ReactNode }) {
  const { user, isLoaded } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (isLoaded && !user) {
      router.replace("/login");
    }
  }, [user, isLoaded, router]);



  if (!isLoaded || !user) {
    return <FullScreenLoader text="Loading your workspace..." />;
  }

  return (
    <main className="min-h-screen bg-muted/40">{children}</main>
  );
}
"use client";
import { cn } from "@/lib/utils";

interface FullScreenLoaderProps {
  text?: string;
  className?: string;
}

export function FullScreenLoader({ 
  text = "Loading...", 
  className 
}: FullScreenLoaderProps) {
  return (
    <div
      className={cn(
        "fixed inset-0 bg-background z-50 flex items-center justify-center",
        className
      )}
    >
      <div className="flex items-center gap-3">
        {/* Same spinner style as sign-in button */}
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
        {text && (
          <span className="text-sm font-medium text-foreground">
            {text}
          </span>
        )}
      </div>
    </div>
  );
}

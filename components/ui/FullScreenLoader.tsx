"use client";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface FullScreenLoaderProps {
  text?: string;
  className?: string;
}

export function FullScreenLoader({ 
  text = "Loading...", 
  className 
}: FullScreenLoaderProps) {
  return (
    <div 
      className={cn(
        "fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center",
        className
      )}
    >
      <div className="flex flex-col items-center gap-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        {text && (
          <p className="text-sm text-muted-foreground font-medium">
            {text}
          </p>
        )}
      </div>
    </div>
  );
}
